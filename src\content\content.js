// Content Script for getting page selection
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log(2222222222, request);
  if (request.method === "getSelection") {
    const selectedText = window.getSelection().toString();
    sendResponse({ data: selectedText });
  } else {
    sendResponse({});
  }
});
// 可以在这里添加更多页面交互功能
console.log('Bark Chrome Extension content script loaded');

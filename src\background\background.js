// Background Script for Manifest V3 (Service Worker)
import { PushService } from "../utils/push";
import { storage, defaultSettings } from "../utils/storage";
const pushService = new PushService();

// 全局推送处理
async function handleGlobalPush(response, tab) {
  try {
    const settings = await storage.get(defaultSettings);

    // 如果默认推送内容是 URL，推送 URL
    if (settings.default_push_content === "URL") {
      if (response?.data && response.data !== '') {
        console.log("send selected text: " + response.data);
        await pushService.sendMessage(response.data);
      } else {
        console.log("send url", tab);
        await sendCurrentUrl();
      }
    } else if (settings.default_push_content === "clipboard") {
      // 如果默认是剪贴板，推送剪贴板数据
      console.log("Attempting to send clipboard data...");
      await sendClipboardData();
    } else {
      console.log("Unknown default push content type:", settings.default_push_content);
    }
  } catch (error) {
    console.error('Error in handleGlobalPush:', error);
  }
}

// 发送当前页面 URL
async function sendCurrentUrl() {
  try {
    const tabs = await chrome.tabs.query({
      active: true,
      lastFocusedWindow: true
    });

    if (tabs.length > 0) {
      const currentUrl = tabs[0].url;
      await pushService.sendMessage(currentUrl);
      console.log('Sent URL:', currentUrl);
    }
  } catch (error) {
    console.error('Error sending URL:', error);
  }
}

// 发送剪贴板数据
async function sendClipboardData() {
  try {
    console.log('Starting clipboard data retrieval...');
    const clipboardData = await getClipboardData();

    if (clipboardData && clipboardData.trim() !== '') {
      console.log('Sending clipboard data:', clipboardData);
      await pushService.sendMessage(clipboardData);
      console.log('Clipboard data sent successfully');
      return { success: true };
    } else {
      console.log('Clipboard is empty or could not be read');
      throw new Error('剪贴板为空或无法读取');
    }
  } catch (error) {
    console.error('Error sending clipboard data:', error);
    throw error; // 重新抛出错误以便上层处理
  }
}

// 获取剪贴板数据 - 在 Manifest V3 中，Service Worker 无法直接访问 navigator.clipboard
// 需要通过 content script 或其他方式获取
async function getClipboardData() {
  try {
    // 尝试通过 active tab 的 content script 获取剪贴板数据
    const tabs = await chrome.tabs.query({
      active: true,
      lastFocusedWindow: true
    });

    if (tabs.length > 0) {
      const tabId = tabs[0].id;
      const tab = tabs[0];

      // 检查是否是特殊页面（chrome://、chrome-extension://等）
      if (tab.url.startsWith('chrome://') ||
          tab.url.startsWith('chrome-extension://') ||
          tab.url.startsWith('moz-extension://') ||
          tab.url.startsWith('edge://')) {
        console.log('Cannot access clipboard on special pages:', tab.url);
        return '';
      }

      // 注入脚本来读取剪贴板
      const results = await chrome.scripting.executeScript({
        target: { tabId: tabId },
        func: async () => {
          try {
            // 检查是否有剪贴板权限
            if (!navigator.clipboard || !navigator.clipboard.readText) {
              console.log('Clipboard API not available');
              return '';
            }

            // 在页面上下文中读取剪贴板
            const text = await navigator.clipboard.readText();
            return text || '';
          } catch (error) {
            console.error('Error reading clipboard in content script:', error);
            // 如果权限被拒绝，返回空字符串而不是错误
            if (error.name === 'NotAllowedError') {
              console.log('Clipboard access denied by user or policy');
            }
            return '';
          }
        }
      });

      if (results && results[0] && results[0].result !== undefined) {
        const clipboardText = results[0].result;
        if (clipboardText) {
          console.log("clipboard content: " + clipboardText);
        } else {
          console.log("clipboard is empty or access denied");
        }
        return clipboardText;
      }
    }

    console.log('No active tab found');
    return '';
  } catch (error) {
    console.error('Error reading clipboard:', error);
    // 如果是权限错误，提供更友好的错误信息
    if (error.message.includes('Cannot access')) {
      console.log('Cannot inject script into this page. Try using the extension on a regular webpage.');
    }
    return '';
  }
}

// 右键菜单处理
async function handleContextMenu(info, tab) {
  console.log("menu " + info.menuItemId + " was clicked.");
  console.log("Word " + info.selectionText + " was clicked.");

  // 如果点击的是 "no-devices" 菜单，打开设置页面
  if (info.menuItemId === "no-devices") {
    chrome.tabs.create({ url: "options.html" });
    return;
  }

  if (info.mediaType === "image") {
    await pushService.sendMessage(info.srcUrl, info.menuItemId, "image");
  } else {
    if (typeof info.selectionText === 'undefined') {
      await handleGlobalPush(null, tab);
    } else {
      await pushService.sendMessage(info.selectionText, info.menuItemId);
    }
  }
} 

// 注册右键菜单
async function registerContextMenus() {
  try {
    const settings = await storage.get(null);
    console.log('=== Context Menu Registration Debug ===');
    console.log('Raw settings:', settings);
    console.log('server_urls:', settings.server_urls);

    // 清除所有现有菜单
    await chrome.contextMenus.removeAll();

    // 如果没有配置设备，创建一个提示菜单
    if (!settings.server_urls || settings.server_urls.length === 0) {
      chrome.contextMenus.create({
        id: "no-devices",
        title: "Bark: 请先配置推送设备",
        contexts: ["all"]
      });
      return;
    }

    // 为每个服务器 URL 创建菜单项
    settings.server_urls.forEach((server, index) => {
      console.log(`Creating menu for device ${index}:`, server);

      // 创建普通推送菜单
      chrome.contextMenus.create({
        id: `${index}`,
        title: `Bark: 推送到 ${server.server_name}`,
        contexts: ["all"]
      });

      // // 创建选择文本和图片的推送菜单
      // chrome.contextMenus.create({
      //   id: `selection#${server.server_url}`,
      //   title: `Bark: 发送到 ${server.server_name}`,
      //   contexts: ["selection", "image"]
      // });
    });

    console.log('=== Context menus registered successfully ===');
  } catch (error) {
    console.error('Error registering context menus:', error);
    console.error('Error stack:', error.stack);
  }
}

// 监听右键菜单点击
chrome.contextMenus.onClicked.addListener(handleContextMenu);

// 监听来自其他脚本的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log(sender.tab ?  "from a content script:" + sender.tab.url :  "from the extension");

  // 处理来自 popup 的推送请求
  if (request.action === "pushClipboard") {
    console.log('Received pushClipboard request from popup');
    sendClipboardData().then(() => {
      console.log('Clipboard push completed successfully');
      sendResponse({ success: true });
    }).catch((error) => {
      console.error('Error pushing clipboard:', error);
      sendResponse({
        success: false,
        error: error.message || '推送失败，请检查网络连接和设置'
      });
    });
    return true; // 保持消息通道开放
  }

  // 处理来自 popup 的 URL 推送请求
  if (request.action === "pushUrl" && request.url) {
    pushService.sendMessage(request.url).then(() => {
      sendResponse({ success: true });
    }).catch((error) => {
      console.error('Error pushing URL:', error);
      sendResponse({ success: false, error: error.message });
    });
    return true; // 保持消息通道开放
  }

  // 处理菜单注册请求
  if (request.greeting === "hello") {
    registerContextMenus().then(() => {
      sendResponse({ farewell: "goodbye" });
    });
    return true; // 保持消息通道开放
  }

  sendResponse({ farewell: "goodbye" });
});

// 扩展启动时注册菜单
chrome.runtime.onStartup.addListener(() => {
  console.log('Extension startup - registering context menus');
  registerContextMenus();
});

chrome.runtime.onInstalled.addListener(() => {
  console.log('Extension installed - registering context menus');
  registerContextMenus();
});
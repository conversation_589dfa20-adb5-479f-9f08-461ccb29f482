<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <!-- 头部 -->
      <div class="card mb-8">
        <div class="flex items-center mb-4">
          <img src="/assets/bark_48.png" alt="Bark" class="w-12 h-12 mr-4">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Bark Extension 设置</h1>
            <p class="text-gray-600 dark:text-gray-400">配置您的推送设备和偏好设置</p>
          </div>
        </div>
      </div>

      <!-- Toast 通知容器 -->
      <div class="fixed top-4 right-4 z-50 space-y-2">
        <div
          v-for="toast in toasts"
          :key="toast.id"
          class="toast-container transform transition-all duration-300 ease-in-out"
          :class="toast.visible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'"
        >
          <div
            class="flex items-center p-4 rounded-lg shadow-lg border max-w-sm"
            :class="getToastClass(toast.type)"
          >
            <!-- 图标 -->
            <div class="flex-shrink-0 mr-3">
              <svg
                v-if="toast.type === 'success'"
                class="w-5 h-5 text-green-600 dark:text-green-400"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              <svg
                v-else
                class="w-5 h-5 text-red-600 dark:text-red-400"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
              </svg>
            </div>
            
            <!-- 消息内容 -->
            <div class="flex-1 text-sm font-medium">
              {{ toast.message }}
            </div>
            
            <!-- 关闭按钮 -->
            <button
              @click="removeToast(toast.id)"
              class="flex-shrink-0 ml-3 p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10 transition-colors"
            >
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Tab 导航和内容 -->
      <div class="card">
        <!-- Tab 导航 -->
        <div class="border-b border-gray-200 dark:border-gray-700">
          <nav class="-mb-px flex space-x-8">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="activeTab = tab.id"
              class="py-4 px-1 border-b-2 font-medium text-sm transition-colors whitespace-nowrap"
              :class="activeTab === tab.id
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'"
            >
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path v-if="tab.id === 'basic'" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  <path v-else-if="tab.id === 'params'" d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                  <path v-else-if="tab.id === 'devices'" d="M17,8C8,10 5.9,16.17 3.82,21.34l1.89,.66 .07-.22C8.84,17.23 12.6,12.31 17,8l3,3h2V3h-8L17,8z"/>
                  <path v-else d="M12,2C6.48,2 2,6.48 2,12s4.48,10 10,10 10,-4.48 10,-10S17.52,2 12,2zM13,17h-2v-6h2v6zM13,9h-2V7h2v2z"/>
                </svg>
                {{ tab.name }}
              </div>
            </button>
          </nav>
        </div>

        <!-- Tab 内容 -->
        <div class="mt-6">
          <!-- 基本设置 Tab -->
          <div v-show="activeTab === 'basic'" class="space-y-6">
            <div>
              <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">默认推送内容</h3>
              <p class="text-gray-600 dark:text-gray-400 mb-4">此选项控制点击工具栏图标时默认推送的内容。</p>
              
              <div class="space-y-3">
                <div class="radio-group">
                  <input 
                    type="radio" 
                    id="clipboard-basic" 
                    v-model="settings.default_push_content" 
                    value="clipboard"
                    class="radio-input"
                    @change="saveSettings"
                  >
                  <label for="clipboard-basic" class="text-gray-700 dark:text-gray-300">剪贴板内容</label>
                </div>

                <div class="radio-group">
                  <input
                    type="radio"
                    id="URL-basic"
                    v-model="settings.default_push_content"
                    value="URL"
                    class="radio-input"
                    @change="saveSettings"
                  >
                  <label for="URL-basic" class="text-gray-700 dark:text-gray-300">当前页面 URL</label>
                </div>
              </div>
            </div>
          </div>

          <!-- 推送参数 Tab -->
          <div v-show="activeTab === 'params'" class="space-y-6">
            <div>
              <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">推送参数配置</h3>
              <p class="text-gray-600 dark:text-gray-400 mb-6">配置 Bark 推送的高级参数，这些参数将应用于所有推送消息。</p>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 基本参数 -->
                <div class="space-y-4">
                  <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">基本参数</h4>
                  
                  <!-- 推送标题 -->
                  <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      推送标题
                    </label>
                    <input
                      type="text"
                      id="title"
                      v-model="settings.push_params.title"
                      @input="saveSettings"
                      placeholder="自定义推送标题（留空使用默认）"
                      class="input-field"
                    >
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">留空将使用默认标题</p>
                  </div>

                  <!-- 推送副标题 -->
                  <div>
                    <label for="subtitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      推送副标题
                    </label>
                    <input
                      type="text"
                      id="subtitle"
                      v-model="settings.push_params.subtitle"
                      @input="saveSettings"
                      placeholder="推送副标题（可选）"
                      class="input-field"
                    >
                  </div>

                  <!-- 推送级别 -->
                  <div>
                    <label for="level" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      推送中断级别
                    </label>
                    <select
                      id="level"
                      v-model="settings.push_params.level"
                      @change="saveSettings"
                      class="input-field"
                    >
                      <option value="active">默认 (active) - 立即亮屏显示</option>
                      <option value="critical">重要警告 (critical) - 静音模式下也响铃</option>
                      <option value="timeSensitive">时效性 (timeSensitive) - 专注状态下显示</option>
                      <option value="passive">被动 (passive) - 仅添加到通知列表</option>
                    </select>
                  </div>

                  <!-- 通知音量 -->
                  <div>
                    <label for="volume" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      通知音量 (0-10)
                    </label>
                    <div class="flex items-center space-x-3">
                      <input
                        type="range"
                        id="volume"
                        v-model.number="settings.push_params.volume"
                        @input="saveSettings"
                        min="0"
                        max="10"
                        class="flex-1"
                      >
                      <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-8">
                        {{ settings.push_params.volume }}
                      </span>
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">仅在重要警告级别下生效</p>
                  </div>

                  
                </div>

                <!-- 高级参数 -->
                <div class="space-y-4">
                  <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">高级参数</h4>

                  <!-- 推送角标 -->
                  <div>
                    <label for="badge" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      推送角标
                    </label>
                    <input
                      type="number"
                      id="badge"
                      v-model.number="settings.push_params.badge"
                      @input="saveSettings"
                      placeholder="角标数字（可选）"
                      class="input-field"
                      min="0"
                    >
                  </div>
                  
                  <!-- 自定义铃声 -->
                  <div>
                    <label for="sound" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      自定义铃声
                    </label>
                    <select
                      id="sound"
                      v-model="settings.push_params.sound"
                      @change="saveSettings"
                      class="input-field"
                    >
                      <option value="">默认铃声</option>
                      <option value="alarm">alarm</option>
                      <option value="anticipate">anticipate</option>
                      <option value="bell">bell</option>
                      <option value="birdsong">birdsong</option>
                      <option value="bloom">bloom</option>
                      <option value="calypso">calypso</option>
                      <option value="chime">chime</option>
                      <option value="choo">choo</option>
                      <option value="descent">descent</option>
                      <option value="electronic">electronic</option>
                      <option value="fanfare">fanfare</option>
                      <option value="glass">glass</option>
                      <option value="gotosleep">gotosleep</option>
                      <option value="healthnotification">healthnotification</option>
                      <option value="horn">horn</option>
                      <option value="ladder">ladder</option>
                      <option value="mailsent">mailsent</option>
                      <option value="minuet">minuet</option>
                      <option value="multiwayinvitation">multiwayinvitation</option>
                      <option value="newmail">newmail</option>
                      <option value="newsflash">newsflash</option>
                      <option value="noir">noir</option>
                      <option value="paymentsuccess">paymentsuccess</option>
                      <option value="shake">shake</option>
                      <option value="sherwoodforest">sherwoodforest</option>
                      <option value="spell">spell</option>
                      <option value="suspense">suspense</option>
                      <option value="telegraph">telegraph</option>
                      <option value="tiptoes">tiptoes</option>
                      <option value="typewriters">typewriters</option>
                      <option value="update">update</option>
                    </select>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">选择预设铃声或使用默认</p>
                  </div>

                  <!-- 自动复制推送内容 -->
                  <div class="flex items-center">
                    <input
                      type="checkbox"
                      id="autoCopyParam"
                      v-model="settings.push_params.autoCopy"
                      @change="saveSettings"
                      class="checkbox-input"
                    >
                    <label for="autoCopyParam" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                      自动复制推送内容到设备剪贴板
                    </label>
                  </div>

                  <!-- 重复播放铃声 -->
                  <div class="flex items-center">
                    <input
                      type="checkbox"
                      id="call"
                      v-model="settings.push_params.call"
                      @change="saveSettings"
                      class="checkbox-input"
                    >
                    <label for="call" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                      重复播放铃声
                    </label>
                  </div>
                </div>
              </div>

              <!-- 更多高级参数 -->
              <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">更多选项</h4>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- 自定义图标 -->
                  <div>
                    <label for="icon" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      自定义图标 URL
                    </label>
                    <input
                      type="url"
                      id="icon"
                      v-model="settings.push_params.icon"
                      @input="saveSettings"
                      placeholder="https://example.com/icon.png"
                      class="input-field"
                    >
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">图标会自动缓存，相同 URL 仅下载一次</p>
                  </div>

                  <!-- 消息分组 -->
                  <div>
                    <label for="group" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      消息分组
                    </label>
                    <input
                      type="text"
                      id="group"
                      v-model="settings.push_params.group"
                      @input="saveSettings"
                      placeholder="分组名称（可选）"
                      class="input-field"
                    >
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">相同分组的消息会聚合显示</p>
                  </div>

                  <!-- 加密密文 -->
                  <div class="md:col-span-2">
                    <label for="ciphertext" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      加密推送密文
                    </label>
                    <textarea
                      id="ciphertext"
                      v-model="settings.push_params.ciphertext"
                      @input="saveSettings"
                      placeholder="加密推送的密文（高级功能）"
                      rows="3"
                      class="input-field resize-none"
                    ></textarea>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">用于加密推送，需要在 Bark 应用中配置相应的解密密钥</p>
                  </div>
                </div>
              </div>

              <!-- 重置按钮 -->
              <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  @click="resetPushParams"
                  class="btn-secondary"
                >
                  重置推送参数
                </button>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">将所有推送参数重置为默认值</p>
              </div>
            </div>
          </div>

          <!-- 设备管理 Tab -->
          <div v-show="activeTab === 'devices'" class="space-y-6">
            <div>
              <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">设备管理</h3>
              <p class="text-gray-600 dark:text-gray-400 mb-6">添加和管理您的 Bark 推送设备。</p>

              <!-- 现有设备列表 -->
              <div v-if="Array.isArray(settings.server_urls) && settings.server_urls.length > 0" class="mb-6">
                <div class="flex items-center justify-between mb-3">
                  <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">已配置的设备</h4>
                  <button
                    @click="testAllDevices"
                    :disabled="isTestingAll"
                    class="btn-secondary text-sm"
                  >
                    <span v-if="!isTestingAll">测试全部设备</span>
                    <span v-else>测试中...</span>
                  </button>
                </div>
                <div class="space-y-3">
                  <div
                    v-for="(server, index) in settings.server_urls"
                    :key="index"
                    class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
                  >
                    <div class="flex-1">
                      <div class="font-medium text-gray-900 dark:text-gray-100">{{ server.server_name }}</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400 break-all">{{ server.server_url }}</div>
                      <!-- 测试状态显示 -->
                      <div v-if="testResults[index]" class="mt-2 flex items-center text-sm" :class="testResults[index].success ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                        <svg v-if="testResults[index].success" class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        <svg v-else class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                        </svg>
                        <span>{{ testResults[index].message }}</span>
                      </div>
                    </div>
                    <div class="flex items-center space-x-2 ml-4">
                      <!-- 测试按钮 -->
                      <button
                        @click="testDevice(index)"
                        :disabled="testingStates[index]"
                        class="p-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-lg transition-colors disabled:opacity-50"
                        title="测试推送"
                      >
                        <svg v-if="!testingStates[index]" class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        <svg v-else class="w-5 h-5 animate-spin" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 4V2A10 10 0 0 0 2 12h2a8 8 0 0 1 8-8z"/>
                        </svg>
                      </button>
                      <!-- 删除按钮 -->
                      <button
                        @click="deleteServer(index)"
                        class="p-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg transition-colors"
                        title="删除设备"
                      >
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M7 21q-.825 0-1.412-.587T5 19V6H4V4h5V3h6v1h5v2h-1v13q0 .825-.587 1.413T17 21zM17 6H7v13h10zM9 17h2V8H9zm4 0h2V8h-2zM7 6v13z"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 添加新设备 -->
              <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
                <h4 class="text-md font-medium mb-4 text-gray-900 dark:text-gray-100">添加新设备</h4>

                <!-- 设备信息输入 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label for="server_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">设备别名</label>
                    <input
                      type="text"
                      id="server_name"
                      v-model="newDevice.name"
                      placeholder="例如：我的iPhone"
                      class="input-field"
                    >
                  </div>

                  <div>
                    <label for="server_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">推送地址</label>
                    <input
                      type="text"
                      id="server_url"
                      v-model="newDevice.url"
                      placeholder="Bark Push URL: https://day.app/your_key/"
                      class="input-field"
                    >
                  </div>
                </div>

                <button
                  @click="addServer"
                  :disabled="!canAddServer"
                  class="btn-primary"
                  :class="{ 'opacity-50 cursor-not-allowed': !canAddServer }"
                >
                  添加设备
                </button>
              </div>
            </div>
          </div>

          <!-- 使用帮助 Tab -->
          <div v-show="activeTab === 'help'" class="space-y-6">
            <div>
              <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">使用提示</h3>
              <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                <li class="flex items-start">
                  <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  如果您设置了多个地址，请使用右键菜单将消息推送到指定设备
                </li>
                <li class="flex items-start">
                  <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  点击扩展图标可以快速推送剪贴板内容或当前页面 URL
                </li>
                <li class="flex items-start">
                  <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  选中文本后右键可以直接推送选中的内容
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { storage, defaultSettings } from '../utils/storage.js'
import { PushService } from '../utils/push.js'
import { validateURL } from '../utils/index.js'
const pushService = new PushService();

// Tab 导航系统
const activeTab = ref('basic')
const tabs = ref([
  { id: 'basic', name: '基本设置' },
  { id: 'params', name: '推送参数' },
  { id: 'devices', name: '设备管理' },
  { id: 'help', name: '使用帮助' }
])

// Toast 通知系统
const toasts = ref([])
let toastIdCounter = 0

// 设置数据
const settings = ref({
  server_urls: [],
  default_push_content: 'clipboard',
  push_params: {
    title: "",
    subtitle: "",
    level: "active",
    volume: 5,
    badge: "",
    call: false,
    autoCopy: false,
    sound: "",
    icon: "",
    group: "",
    ciphertext: ""
  }
})

const newDevice = ref({
  name: '',
  url: ''
})

// 测试相关的响应式数据
const testingStates = ref({}) // 记录每个设备的测试状态
const testResults = ref({}) // 记录每个设备的测试结果
const isTestingAll = ref(false) // 是否正在测试全部设备

// 计算属性
const canAddServer = computed(() => {
  return newDevice.value.name.trim() && newDevice.value.url.trim()
})

// 加载设置
const loadSettings = async () => {
  try {
    // 获取所有已保存的数据
    const savedSettings = await storage.get(null) // null 表示获取所有数据

    console.log('Loaded raw data:', savedSettings)
    console.log('savedSettings.server_urls:', savedSettings.server_urls)
    console.log('Is array:', Array.isArray(savedSettings.server_urls))
    console.log('Type:', typeof savedSettings.server_urls)

    // 确保 server_urls 是数组，并保持响应式
    let loadedServerUrls = defaultSettings.server_urls

    if (savedSettings.server_urls) {
      if (Array.isArray(savedSettings.server_urls)) {
        loadedServerUrls = savedSettings.server_urls
      } else {
        // 如果是对象，尝试转换为数组
        console.warn('server_urls is not an array, attempting to convert:', savedSettings.server_urls)
        const urlsArray = Object.values(savedSettings.server_urls)
        if (urlsArray.length > 0 && urlsArray[0].server_name && urlsArray[0].server_url) {
          loadedServerUrls = urlsArray
          console.log('Converted to array:', loadedServerUrls)
        }
      }
    }

    console.log('Final loadedServerUrls:', loadedServerUrls)

    // 清空现有数组并添加新数据，确保响应式
    settings.value.server_urls.splice(0, settings.value.server_urls.length, ...loadedServerUrls)

    console.log('Updated settings.value.server_urls:', settings.value.server_urls)
    console.log('Is array after update:', Array.isArray(settings.value.server_urls))
    settings.value.default_push_content = savedSettings.default_push_content ?? defaultSettings.default_push_content

    // 加载推送参数，合并默认值
    settings.value.push_params = {
      ...defaultSettings.push_params,
      ...(savedSettings.push_params || {})
    }

    console.log('Loaded push_params:', settings.value.push_params)
  } catch (error) {
    console.error('Load settings failed:', error)
    // 如果加载失败，重置为默认设置
    settings.value.server_urls = []
    settings.value.default_push_content = defaultSettings.default_push_content
    settings.value.push_params = { ...defaultSettings.push_params }
    showStatus('加载设置失败，使用默认设置', 'error')
  }
}

// 保存设置
const saveSettings = async () => {
  try {
    // 确保数据结构正确
    const dataToSave = {
      server_urls: Array.isArray(settings.value.server_urls) ? [...settings.value.server_urls] : [],
      default_push_content: settings.value.default_push_content || 'clipboard',
      push_params: { ...settings.value.push_params }
    }

    console.log('Saving data:', dataToSave)
    console.log('server_urls is array:', Array.isArray(dataToSave.server_urls))

    await storage.set(dataToSave)
    console.log('Settings saved successfully')
    showStatus('保存成功', 'success')
  } catch (error) {
    console.error('Save settings failed:', error)
    showStatus('保存设置失败', 'error')
  }
}

// 重置推送参数
const resetPushParams = async () => {
  try {
    settings.value.push_params = { ...defaultSettings.push_params }
    await saveSettings()
    showStatus('推送参数已重置为默认值', 'success')
  } catch (error) {
    console.error('Reset push params failed:', error)
    showStatus('重置推送参数失败', 'error')
  }
}

// Toast 通知系统函数
const showStatus = (message, type = 'success') => {
  const id = ++toastIdCounter
  const toast = {
    id,
    message,
    type,
    visible: false
  }

  toasts.value.push(toast)

  // 延迟显示动画
  nextTick(() => {
    toast.visible = true
  })

  // 自动移除
  setTimeout(() => {
    removeToast(id)
  }, 4000)
}

const removeToast = (id) => {
  const index = toasts.value.findIndex(toast => toast.id === id)
  if (index > -1) {
    toasts.value[index].visible = false
    // 等待动画完成后移除
    setTimeout(() => {
      const currentIndex = toasts.value.findIndex(toast => toast.id === id)
      if (currentIndex > -1) {
        toasts.value.splice(currentIndex, 1)
      }
    }, 300)
  }
}

const getToastClass = (type) => {
  return type === 'success'
    ? 'bg-green-50 dark:bg-green-900/30 text-green-800 dark:text-green-300 border-green-200 dark:border-green-700'
    : 'bg-red-50 dark:bg-red-900/30 text-red-800 dark:text-red-300 border-red-200 dark:border-red-700'
}

// 添加服务器
const addServer = async () => {
  if (!canAddServer.value) return

  // 验证 URL
  if (!validateURL(newDevice.value.url)) {
    showStatus('请输入有效的 URL', 'error')
    return
  }

  const newServer = {
    server_name: newDevice.value.name.trim(),
    server_url: newDevice.value.url.trim()
  }

  // 确保 server_urls 是数组
  if (!Array.isArray(settings.value.server_urls)) {
    settings.value.server_urls = []
  }

  settings.value.server_urls.push(newServer)
  await saveSettings()

  // 清空表单
  newDevice.value.name = ''
  newDevice.value.url = ''

  showStatus('设备添加成功', 'success')
}

// 删除服务器
const deleteServer = async (index) => {
  if (confirm('确定要删除这个设备吗？')) {
    // 确保 server_urls 是数组
    if (!Array.isArray(settings.value.server_urls)) {
      settings.value.server_urls = []
      return
    }

    settings.value.server_urls.splice(index, 1)
    await saveSettings()
    showStatus('设备已删除', 'success')
  }
}

// 测试设备推送功能
const testDevice = async (index) => {
  const server = settings.value.server_urls[index]
  if (!server) return

  // 设置测试状态
  testingStates.value[index] = true
  testResults.value[index] = null

  try {
    const testMessage = `测试消息 - ${new Date().toLocaleTimeString()}`

    // 直接发送 HTTP 请求
    const serverUrl = server.server_url

    pushService.sendMessage(testMessage, {serverUrl});

    // 测试成功
    testResults.value[index] = {
      success: true,
      message: '测试成功！请检查设备是否收到通知'
    }

  } catch (error) {
    // 测试失败
    testResults.value[index] = {
      success: false,
      message: `测试失败：${error.message || '网络错误或设备配置有误'}`
    }
  } finally {
    // 清除测试状态
    testingStates.value[index] = false

    // 5秒后清除测试结果
    setTimeout(() => {
      if (testResults.value[index]) {
        testResults.value[index] = null
      }
    }, 5000)
  }
}

// 测试全部设备
const testAllDevices = async () => {
  if (!Array.isArray(settings.value.server_urls) || settings.value.server_urls.length === 0) {
    showStatus('没有可测试的设备', 'error')
    return
  }

  isTestingAll.value = true

  try {
    // 并行测试所有设备
    const testPromises = settings.value.server_urls.map((_, index) => testDevice(index))
    await Promise.allSettled(testPromises)

    // 统计测试结果
    const successCount = Object.values(testResults.value).filter(result => result?.success).length
    const totalCount = settings.value.server_urls.length

    if (successCount === totalCount) {
      showStatus(`全部 ${totalCount} 个设备测试成功`, 'success')
    } else {
      showStatus(`${successCount}/${totalCount} 个设备测试成功`, 'error')
    }
  } finally {
    isTestingAll.value = false
  }
}

// 页面加载时初始化
onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.toast-container {
  backdrop-filter: blur(8px);
}

/* 确保 toast 在所有内容之上 */
.toast-container > div {
  backdrop-filter: blur(8px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 深色模式下的阴影调整 */
@media (prefers-color-scheme: dark) {
  .toast-container > div {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  }
}

/* 动画优化 */
.toast-container {
  will-change: transform, opacity;
}
</style>
